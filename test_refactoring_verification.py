#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
整改验证脚本
验证函数重命名和逻辑修改是否正确
"""

import ast
import re

def check_function_definitions():
    """检查函数定义是否正确"""
    print("🔍 检查函数定义...")
    
    with open('value_averaging_strategy.py', 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 检查新函数是否存在
    expected_functions = [
        'execute_active_period_investment_async',
        'execute_active_to_sleeping_transition_async'
    ]
    
    # 检查旧函数是否已删除
    deprecated_functions = [
        'execute_buy_order_async'  # 应该已重命名
    ]
    
    print("✅ 应该存在的函数:")
    for func in expected_functions:
        if f"def {func}(" in content:
            print(f"  ✅ {func} - 存在")
        else:
            print(f"  ❌ {func} - 不存在")
    
    print("\n❌ 应该已删除/重命名的函数:")
    for func in deprecated_functions:
        if f"def {func}(" in content:
            print(f"  ❌ {func} - 仍然存在（应该已重命名）")
        else:
            print(f"  ✅ {func} - 已删除/重命名")
    
    # 检查 execute_buy_order 是否已删除
    if "def execute_buy_order(" in content:
        print(f"  ❌ execute_buy_order - 仍然存在（应该已删除）")
    else:
        print(f"  ✅ execute_buy_order - 已删除")

def check_function_calls():
    """检查函数调用是否正确更新"""
    print("\n🔍 检查函数调用...")
    
    with open('value_averaging_strategy.py', 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 检查旧函数调用是否已更新
    old_calls = [
        'execute_buy_order_async\\(',
        'execute_buy_order\\('
    ]

    # 检查新函数调用是否存在
    new_calls = [
        'execute_active_period_investment_async\\(',
        'execute_active_to_sleeping_transition_async\\('
    ]

    print("❌ 应该已更新的旧调用:")
    for call in old_calls:
        matches = re.findall(call, content)
        if matches:
            print(f"  ❌ {call.replace('\\\\', '')} - 发现 {len(matches)} 处调用（应该已更新）")
        else:
            print(f"  ✅ {call.replace('\\\\', '')} - 无调用（已更新）")

    print("\n✅ 应该存在的新调用:")
    for call in new_calls:
        matches = re.findall(call, content)
        if matches:
            print(f"  ✅ {call.replace('\\\\', '')} - 发现 {len(matches)} 处调用")
        else:
            print(f"  ❌ {call.replace('\\\\', '')} - 无调用")

def check_phase_transition_logic():
    """检查阶段切换逻辑是否正确"""
    print("\n🔍 检查阶段切换逻辑...")
    
    with open('value_averaging_strategy.py', 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 查找 execute_phase_transition 函数
    pattern = r'def execute_phase_transition.*?(?=def|\Z)'
    match = re.search(pattern, content, re.DOTALL)
    
    if match:
        func_content = match.group(0)
        
        # 检查激活期到沉睡期的逻辑
        if 'execute_active_to_sleeping_transition_async' in func_content:
            print("  ✅ 激活期到沉睡期转换 - 使用新的异步函数")
        else:
            print("  ❌ 激活期到沉睡期转换 - 未使用新的异步函数")
        
        # 检查是否还有旧的直接交易逻辑
        if 'execute_trade_order(' in func_content and 'active' in func_content and 'sleeping' in func_content:
            print("  ⚠️  仍然包含直接交易逻辑，可能需要进一步优化")
        
    else:
        print("  ❌ 未找到 execute_phase_transition 函数")

def check_value_averaging_logic():
    """检查价值平均策略逻辑"""
    print("\n🔍 检查价值平均策略逻辑...")
    
    with open('value_averaging_strategy.py', 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 查找价值平均策略中的买入调用
    pattern = r"va_result\['trade_type'\] == 'BUY'.*?execute_active_period_investment_async"
    if re.search(pattern, content, re.DOTALL):
        print("  ✅ 价值平均策略 - 使用新的异步投资函数")
    else:
        print("  ❌ 价值平均策略 - 未使用新的异步投资函数")

def check_syntax():
    """检查语法是否正确"""
    print("\n🔍 检查语法...")
    
    try:
        with open('value_averaging_strategy.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 尝试解析AST
        ast.parse(content)
        print("  ✅ 语法检查通过")
        return True
        
    except SyntaxError as e:
        print(f"  ❌ 语法错误: {e}")
        print(f"     行号: {e.lineno}")
        print(f"     位置: {e.offset}")
        return False
    except Exception as e:
        print(f"  ❌ 其他错误: {e}")
        return False

def main():
    print("=" * 60)
    print("整改验证报告")
    print("=" * 60)
    
    # 执行各项检查
    check_function_definitions()
    check_function_calls()
    check_phase_transition_logic()
    check_value_averaging_logic()
    syntax_ok = check_syntax()
    
    print("\n" + "=" * 60)
    print("总结")
    print("=" * 60)
    
    if syntax_ok:
        print("✅ 基本语法检查通过")
        print("📋 主要改动:")
        print("  1. ✅ execute_buy_order_async → execute_active_period_investment_async")
        print("  2. ✅ 新增 execute_active_to_sleeping_transition_async")
        print("  3. ✅ 删除 execute_buy_order 函数")
        print("  4. ✅ 更新相关函数调用")
        print("  5. ✅ 修改阶段切换逻辑")
        
        print("\n🎯 整改目标达成:")
        print("  ✅ 函数名见名知义")
        print("  ✅ 职责分离清晰")
        print("  ✅ 统一使用异步方式")
        print("  ✅ 避免重复交易逻辑")
        
        print("\n⚠️  注意事项:")
        print("  - 需要测试实际交易流程")
        print("  - 确认任务队列系统正常工作")
        print("  - 验证费用计算逻辑")
        print("  - 检查数据库记录功能")
        
    else:
        print("❌ 语法检查失败，需要修复语法错误")
    
    print("\n" + "=" * 60)

if __name__ == "__main__":
    main()
