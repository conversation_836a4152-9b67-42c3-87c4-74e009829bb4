#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试成交回调逻辑的简化脚本
验证我们添加的调试日志是否正常工作
"""

import sqlite3
import datetime
import uuid

DATABASE_PATH = "gytrading2.db"

def calculate_trading_fees(amount: float, shares: int, trade_type: str, stock_code: str = None) -> dict:
    """计算交易费用"""
    COMMISSION_FEE_RATE = 0.0003
    COMMISSION_FEE_MIN = 5
    SELL_TAX_RATE = 0.001
    TRANSFER_FEE_RATE = 0.00002
    
    # 佣金（买卖都收取）
    commission = max(amount * COMMISSION_FEE_RATE, COMMISSION_FEE_MIN)

    # 印花税（仅卖出时收取）
    stamp_tax = amount * SELL_TAX_RATE if trade_type == 'SELL' else 0.0

    # 过户费（根据股票代码判断）
    transfer_fee = 0.0
    if stock_code:
        # 上海股票（以6开头或者.SH结尾）收取过户费
        if stock_code.startswith('6') or stock_code.endswith('.SH'):
            transfer_fee = max(amount * TRANSFER_FEE_RATE, 1.0)  # 最低1元
        # 深圳股票（以0、2、3开头或者.SZ结尾）免收过户费
        elif stock_code.startswith(('0', '2', '3')) or stock_code.endswith('.SZ'):
            transfer_fee = 0.0

    total_fees = commission + stamp_tax + transfer_fee

    # 计算净金额
    if trade_type == 'BUY':
        net_amount = -(amount + total_fees)  # 买入总支出
    else:
        net_amount = amount - total_fees     # 卖出净收入

    return {
        'commission': commission,
        'stamp_tax': stamp_tax,
        'transfer_fee': transfer_fee,
        'total_fees': total_fees,
        'net_amount': net_amount,
        'gross_amount': amount
    }

def simulate_callback_logic():
    """模拟成交回调逻辑"""
    try:
        conn = sqlite3.connect(DATABASE_PATH)
        cursor = conn.cursor()
        current_time = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        
        print("【调试表中无数据】开始模拟成交回调逻辑测试...")
        
        # 1. 创建测试任务
        task_group_id = str(uuid.uuid4())
        order_uuid = str(uuid.uuid4())
        
        print(f"【调试表中无数据】创建测试任务，UUID={order_uuid}")
        
        cursor.execute("""
            INSERT INTO trade_task_queue
            (task_group_id, task_type, stock_code, target_shares, target_amount,
             estimated_price, estimated_fees, task_status, order_uuid, order_id, created_time)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        """, (task_group_id, "BUY_159915_CASH", "159915.SZ", 1000, 2500.0,
              2.5, 5.0, "WAITING_CALLBACK", order_uuid, "12345", current_time))
        
        task_id = cursor.lastrowid
        
        # 2. 模拟成交数据
        deal_shares = 1000
        deal_price = 2.5
        deal_amount = deal_shares * deal_price
        
        print(f"【调试表中无数据】模拟成交数据：{deal_shares}股，价格{deal_price}，金额{deal_amount}")
        
        # 3. 模拟回调逻辑中的数据插入过程
        print(f"【调试表中无数据】开始处理成交回调数据插入，任务ID={task_id}")
        
        # 查询任务信息
        cursor.execute("""
            SELECT task_type, stock_code, order_uuid, order_id
            FROM trade_task_queue
            WHERE id = ?
        """, (task_id,))
        task_info = cursor.fetchone()
        print(f"【调试表中无数据】查询任务信息结果：{task_info}")
        
        if task_info:
            task_type, stock_code, order_uuid_db, order_id = task_info
            trade_type = "BUY" if "BUY" in task_type else "SELL"
            print(f"【调试表中无数据】解析任务信息：类型={trade_type}, 股票={stock_code}, UUID={order_uuid_db}")
            
            # 计算费用详情
            fee_details = calculate_trading_fees(deal_amount, deal_shares, trade_type, stock_code)
            print(f"【调试表中无数据】计算费用详情：{fee_details}")
            
            # 插入 trade_execution_log
            print(f"【调试表中无数据】开始插入 trade_execution_log，UUID={order_uuid_db}, 类型={trade_type}, 股票={stock_code}")
            
            cursor.execute("""
                INSERT INTO trade_execution_log
                (trade_time, trade_type, stock_code, shares, price, amount, fees,
                 order_id, order_uuid, status, created_time)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """, (current_time, trade_type, stock_code, deal_shares, deal_price, deal_amount,
                  fee_details['total_fees'], str(order_id), order_uuid_db, 'SUCCESS', current_time))
            
            execution_log_id = cursor.lastrowid
            print(f"【调试表中无数据】trade_execution_log 插入成功，记录ID={execution_log_id}")
            
            # 插入 trade_fee_details
            print(f"【调试表中无数据】开始插入 trade_fee_details，关联ID={execution_log_id}")
            cursor.execute("""
                INSERT INTO trade_fee_details
                (execution_log_id, order_uuid, commission, stamp_tax, transfer_fee,
                 other_fees, total_fees, net_amount, created_time)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
            """, (execution_log_id, order_uuid_db, fee_details['commission'], fee_details['stamp_tax'],
                  fee_details['transfer_fee'], 0.0, fee_details['total_fees'], fee_details['net_amount'], current_time))
            
            print(f"【调试表中无数据】trade_fee_details 插入成功")
            
            # 插入 position_records
            print(f"【调试表中无数据】开始插入 position_records，股票={stock_code}, 股数={deal_shares}, 价格={deal_price}")
            
            market_value = deal_shares * deal_price
            cursor.execute("""
                INSERT INTO position_records
                (record_date, stock_code, shares, avg_cost, market_value, current_price,
                 period_number, target_value, created_time)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
            """, (current_time, stock_code, deal_shares, deal_price, market_value, deal_price,
                  1, 2500.0, current_time))
            
            print(f"【调试表中无数据】position_records 插入成功")
            
            # 更新 account_info
            print(f"【调试表中无数据】开始更新 account_info 表")
            
            cursor.execute("""
                INSERT OR REPLACE INTO account_info
                (account_id, total_assets, available_cash, credit_limit, credit_available,
                 update_time, created_time)
                VALUES (?, ?, ?, ?, ?, ?, 
                        COALESCE((SELECT created_time FROM account_info WHERE account_id = ?), ?))
            """, ("callback_test_account", 100000.0, 95000.0, 50000.0, 50000.0, current_time,
                  "callback_test_account", current_time))
            
            print(f"【调试表中无数据】account_info 更新成功")
            
            # 提交所有更改
            conn.commit()
            
            print(f"【调试表中无数据】✅ 所有数据插入完成")
            
            # 验证结果
            print(f"【调试表中无数据】验证插入结果：")
            tables = ['trade_execution_log', 'trade_fee_details', 'position_records', 'account_info']
            for table in tables:
                cursor.execute(f"SELECT COUNT(*) FROM {table}")
                count = cursor.fetchone()[0]
                print(f"【调试表中无数据】{table}: {count} 条记录")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"【调试表中无数据】❌ 模拟回调逻辑失败：{str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("=" * 60)
    print("成交回调逻辑测试")
    print("=" * 60)
    
    if simulate_callback_logic():
        print("\n【调试表中无数据】✅ 测试成功！回调逻辑验证通过")
    else:
        print("\n【调试表中无数据】❌ 测试失败！需要进一步检查")
    
    print("\n" + "=" * 60)
