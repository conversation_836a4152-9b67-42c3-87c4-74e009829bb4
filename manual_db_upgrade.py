#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
手动执行数据库升级
"""

import sqlite3
import os

DATABASE_PATH = "gytrading2.db"

def manual_upgrade():
    """手动升级数据库结构"""
    try:
        print("开始手动升级数据库结构...")
        
        if not os.path.exists(DATABASE_PATH):
            print(f"❌ 数据库文件不存在：{DATABASE_PATH}")
            return False
        
        conn = sqlite3.connect(DATABASE_PATH)
        cursor = conn.cursor()
        
        # 检查 trade_orders 表是否已有 order_uuid 字段
        cursor.execute("PRAGMA table_info(trade_orders)")
        columns = [column[1] for column in cursor.fetchall()]
        
        print(f"当前 trade_orders 表字段：{columns}")
        
        if 'order_uuid' not in columns:
            print("添加 order_uuid 字段...")
            cursor.execute("ALTER TABLE trade_orders ADD COLUMN order_uuid TEXT")
            
            print("创建 order_uuid 索引...")
            cursor.execute("CREATE INDEX IF NOT EXISTS idx_trade_orders_uuid ON trade_orders(order_uuid)")
            
            conn.commit()
            print("✅ order_uuid 字段和索引已添加")
        else:
            print("✅ order_uuid 字段已存在")
        
        # 验证升级结果
        cursor.execute("PRAGMA table_info(trade_orders)")
        new_columns = [column[1] for column in cursor.fetchall()]
        print(f"升级后 trade_orders 表字段：{new_columns}")
        
        # 检查索引
        cursor.execute("PRAGMA index_list(trade_orders)")
        indexes = cursor.fetchall()
        print(f"trade_orders 表索引：{indexes}")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ 数据库升级失败：{str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_insert_with_uuid():
    """测试插入带 UUID 的记录"""
    try:
        print("\n测试插入带 UUID 的记录...")
        
        conn = sqlite3.connect(DATABASE_PATH)
        cursor = conn.cursor()
        
        import datetime
        import uuid
        
        current_time = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        test_uuid = f"test-{str(uuid.uuid4())}"
        
        # 插入测试记录
        cursor.execute("""
            INSERT INTO trade_orders
            (order_date, stock_code, order_type, order_reason, target_shares,
             order_status, order_uuid, created_time)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?)
        """, (current_time, "159915.SZ", "BUY", "TEST", 1000,
              "PENDING", test_uuid, current_time))
        
        order_id = cursor.lastrowid
        print(f"✅ 插入成功，订单ID：{order_id}")
        
        # 验证插入
        cursor.execute("""
            SELECT order_uuid, stock_code, order_type, target_shares
            FROM trade_orders 
            WHERE id = ?
        """, (order_id,))
        
        result = cursor.fetchone()
        if result:
            stored_uuid, stock_code, order_type, shares = result
            print(f"✅ 验证成功：UUID={stored_uuid}, 股票={stock_code}, 类型={order_type}, 股数={shares}")
        
        # 测试通过 UUID 查询
        cursor.execute("""
            SELECT id, stock_code FROM trade_orders 
            WHERE order_uuid = ?
        """, (test_uuid,))
        
        result = cursor.fetchone()
        if result:
            found_id, found_stock = result
            print(f"✅ UUID 查询成功：ID={found_id}, 股票={found_stock}")
        
        # 清理测试数据
        cursor.execute("DELETE FROM trade_orders WHERE id = ?", (order_id,))
        conn.commit()
        conn.close()
        
        print("✅ 测试完成")
        return True
        
    except Exception as e:
        print(f"❌ 测试失败：{str(e)}")
        import traceback
        traceback.print_exc()
        return False

def main():
    print("=" * 50)
    print("手动数据库升级工具")
    print("=" * 50)
    
    # 1. 执行升级
    if manual_upgrade():
        # 2. 测试功能
        test_insert_with_uuid()
    
    print("\n" + "=" * 50)
    print("升级完成")
    print("=" * 50)

if __name__ == "__main__":
    main()
