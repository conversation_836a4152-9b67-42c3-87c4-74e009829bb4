#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化的 order_uuid 集成测试
"""

import sqlite3
import datetime
import uuid

DATABASE_PATH = "gytrading2.db"

def test_record_trade_order_with_uuid():
    """测试带 UUID 的订单记录"""
    print("=" * 50)
    print("测试带 UUID 的订单记录")
    print("=" * 50)
    
    try:
        conn = sqlite3.connect(DATABASE_PATH)
        cursor = conn.cursor()
        
        current_time = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        test_uuid = f"test-record-{str(uuid.uuid4())}"
        
        # 模拟 record_trade_order 函数的逻辑
        cursor.execute("""
            INSERT INTO trade_orders
            (order_date, stock_code, order_type, order_reason, target_shares,
             order_status, order_uuid, created_time)
            VALUES (?, ?, ?, ?, ?, 'PENDING', ?, ?)
        """, (current_time, "159915.SZ", "BUY", "TEST_RECORD", 1000, test_uuid, current_time))
        
        order_id = cursor.lastrowid
        conn.commit()
        
        print(f"✅ 订单记录成功，ID: {order_id}, UUID: {test_uuid}")
        
        # 验证记录
        cursor.execute("""
            SELECT order_uuid, stock_code, order_type, target_shares, order_status
            FROM trade_orders 
            WHERE id = ?
        """, (order_id,))
        
        result = cursor.fetchone()
        if result:
            stored_uuid, stock_code, order_type, shares, status = result
            print(f"✅ 验证成功：UUID={stored_uuid}, 股票={stock_code}, 类型={order_type}, 股数={shares}, 状态={status}")
        
        # 清理
        cursor.execute("DELETE FROM trade_orders WHERE id = ?", (order_id,))
        conn.commit()
        conn.close()
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败：{str(e)}")
        return False

def test_order_error_callback_logic():
    """测试订单错误回调逻辑"""
    print("\n" + "=" * 50)
    print("测试订单错误回调逻辑")
    print("=" * 50)
    
    try:
        conn = sqlite3.connect(DATABASE_PATH)
        cursor = conn.cursor()
        
        current_time = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        test_uuid = f"test-error-{str(uuid.uuid4())}"
        
        # 1. 创建 trade_task_queue 记录
        cursor.execute("""
            INSERT INTO trade_task_queue
            (task_group_id, task_type, stock_code, target_shares, target_amount,
             estimated_price, estimated_fees, task_status, order_uuid, order_id, created_time)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        """, (str(uuid.uuid4()), "BUY_159915_CASH", "159915.SZ", 1000, 2500.0,
              2.5, 5.0, "WAITING_CALLBACK", test_uuid, "TEST12345", current_time))
        
        task_id = cursor.lastrowid
        
        # 2. 创建 trade_orders 记录
        cursor.execute("""
            INSERT INTO trade_orders
            (order_date, stock_code, order_type, order_reason, target_shares,
             order_status, order_uuid, created_time)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?)
        """, (current_time, "159915.SZ", "BUY", "TEST_ERROR", 1000,
              "PENDING", test_uuid, current_time))
        
        order_id = cursor.lastrowid
        conn.commit()
        
        print(f"✅ 测试数据创建成功：task_id={task_id}, order_id={order_id}, uuid={test_uuid}")
        
        # 3. 模拟错误回调处理逻辑
        error_msg = "测试错误：资金不足"
        
        # 更新 trade_task_queue
        cursor.execute("""
            UPDATE trade_task_queue 
            SET task_status = 'FAILED',
                error_message = ?
            WHERE order_uuid = ? AND task_status = 'WAITING_CALLBACK'
        """, (error_msg, test_uuid))
        
        task_updated = cursor.rowcount
        
        # 更新 trade_orders
        cursor.execute("""
            UPDATE trade_orders 
            SET order_status = 'FAILED',
                error_message = ?
            WHERE order_uuid = ? AND order_status = 'PENDING'
        """, (error_msg, test_uuid))
        
        order_updated = cursor.rowcount
        conn.commit()
        
        print(f"✅ 状态更新成功：task_updated={task_updated}, order_updated={order_updated}")
        
        # 4. 验证结果
        cursor.execute("""
            SELECT task_status, error_message FROM trade_task_queue 
            WHERE order_uuid = ?
        """, (test_uuid,))
        
        task_result = cursor.fetchone()
        if task_result:
            status, error = task_result
            if status == "FAILED":
                print("✅ trade_task_queue 状态更新正确")
            else:
                print(f"❌ trade_task_queue 状态错误：{status}")
        
        cursor.execute("""
            SELECT order_status, error_message FROM trade_orders 
            WHERE order_uuid = ?
        """, (test_uuid,))
        
        order_result = cursor.fetchone()
        if order_result:
            status, error = order_result
            if status == "FAILED":
                print("✅ trade_orders 状态更新正确")
            else:
                print(f"❌ trade_orders 状态错误：{status}")
        
        # 清理
        cursor.execute("DELETE FROM trade_task_queue WHERE order_uuid = ?", (test_uuid,))
        cursor.execute("DELETE FROM trade_orders WHERE order_uuid = ?", (test_uuid,))
        conn.commit()
        conn.close()
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败：{str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_order_success_callback_logic():
    """测试订单成功回调逻辑"""
    print("\n" + "=" * 50)
    print("测试订单成功回调逻辑")
    print("=" * 50)
    
    try:
        conn = sqlite3.connect(DATABASE_PATH)
        cursor = conn.cursor()
        
        current_time = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        test_uuid = f"test-success-{str(uuid.uuid4())}"
        
        # 1. 创建测试数据
        cursor.execute("""
            INSERT INTO trade_orders
            (order_date, stock_code, order_type, order_reason, target_shares,
             order_status, order_uuid, created_time)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?)
        """, (current_time, "159915.SZ", "BUY", "TEST_SUCCESS", 1000,
              "PENDING", test_uuid, current_time))
        
        order_id = cursor.lastrowid
        conn.commit()
        
        print(f"✅ 测试数据创建成功：order_id={order_id}, uuid={test_uuid}")
        
        # 2. 模拟成功回调处理逻辑
        actual_shares = 1000
        actual_price = 2.51
        execution_time = current_time
        
        # 更新 trade_orders（模拟成交回调）
        cursor.execute("""
            UPDATE trade_orders
            SET order_status = 'SUCCESS',
                actual_shares = ?,
                actual_price = ?,
                execution_time = ?
            WHERE order_uuid = ? AND order_status IN ('PENDING', 'PARTIAL')
        """, (actual_shares, actual_price, execution_time, test_uuid))
        
        order_updated = cursor.rowcount
        conn.commit()
        
        print(f"✅ 成交信息更新成功：order_updated={order_updated}")
        
        # 3. 验证结果
        cursor.execute("""
            SELECT order_status, actual_shares, actual_price, execution_time
            FROM trade_orders 
            WHERE order_uuid = ?
        """, (test_uuid,))
        
        result = cursor.fetchone()
        if result:
            status, shares, price, exec_time = result
            print(f"✅ 验证成功：状态={status}, 股数={shares}, 价格={price}, 时间={exec_time}")
            
            if status == "SUCCESS":
                print("✅ 订单状态更新正确")
            else:
                print(f"❌ 订单状态错误：{status}")
        
        # 清理
        cursor.execute("DELETE FROM trade_orders WHERE order_uuid = ?", (test_uuid,))
        conn.commit()
        conn.close()
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败：{str(e)}")
        import traceback
        traceback.print_exc()
        return False

def main():
    print("order_uuid 字段集成测试")
    print("验证数据库升级后的功能")
    
    results = []
    
    # 测试订单记录
    results.append(("订单记录功能", test_record_trade_order_with_uuid()))
    
    # 测试错误回调
    results.append(("错误回调处理", test_order_error_callback_logic()))
    
    # 测试成功回调
    results.append(("成功回调处理", test_order_success_callback_logic()))
    
    # 输出结果
    print("\n" + "=" * 50)
    print("测试结果汇总")
    print("=" * 50)
    
    all_passed = True
    for test_name, passed in results:
        status = "✅ 通过" if passed else "❌ 失败"
        print(f"{test_name:<20} {status}")
        if not passed:
            all_passed = False
    
    print("=" * 50)
    if all_passed:
        print("🎉 所有测试通过！order_uuid 集成成功！")
        print("\n关键改进：")
        print("1. ✅ trade_orders 表已添加 order_uuid 字段")
        print("2. ✅ 订单记录时可以关联 UUID")
        print("3. ✅ 错误回调可以精确更新订单状态")
        print("4. ✅ 成功回调可以精确更新成交信息")
        print("5. ✅ 数据一致性得到保障")
    else:
        print("⚠️ 部分测试失败，请检查相关功能")

if __name__ == "__main__":
    main()
