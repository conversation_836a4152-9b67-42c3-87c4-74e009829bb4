# 融资买入逻辑冲突修复报告

## 问题描述

用户发现 `_execute_buy_159915_logic` 函数和 `TradeTaskExecutor.execute_buy_margin_task` 之间存在逻辑冲突：

### 原始问题

1. **创建现金买入任务时**：
   - 不管现金是否足够，都按满份额创建现金买入任务（`target_shares = 1000`）

2. **创建融资买入任务时**：
   - 如果现金可能不足，创建 `target_shares = 0` 的融资任务
   - 期望后续根据现金买入结果计算剩余份额

3. **现金买入执行后**：
   - 会更新 `target_shares` 为实际买入股数（如 600 股）
   - 但如果现金不足连最小单位都买不到，任务直接完成且 `target_shares` 保持原值

4. **融资买入执行时的错误逻辑**：
   - `get_original_target_shares()` 从现金买入任务的 `target_shares` 获取"原始目标"
   - 但此时 `target_shares` 已被修改为实际买入股数
   - 导致：`remaining_shares = actual_shares - actual_shares = 0`
   - 结果：融资买入任务不会执行

## 修复方案

采用**方案1：保存原始目标份额**，具体实施：

### 1. 修改任务创建逻辑

在 `_execute_buy_159915_logic` 函数中：

```python
# 现金买入任务
buy_cash_task_id, buy_order_uuid = g_trade_task_queue.create_task(
    # ... 其他参数 ...
    task_params={'reason': order_reason, 'use_all_cash': True, 'original_target_shares': target_shares},
    # ...
)

# 融资买入任务
buy_margin_task_id, buy_order_uuid = g_trade_task_queue.create_task(
    # ... 其他参数 ...
    task_params={'reason': order_reason, 'buy_remaining': True, 'original_target_shares': target_shares},
    # ...
)
```

### 2. 修改融资买入执行逻辑

在 `execute_buy_margin_task` 函数中：

```python
# 从任务参数获取原始目标份额（而不是从数据库查询）
task_params = task.get('task_params', {})
original_target = task_params.get('original_target_shares', 0)

# 从 trade_task_log 表查询现金买入的实际股数
cursor.execute("""
    SELECT actual_shares FROM trade_task_log
    WHERE task_id = ? AND execution_step = 'EXECUTION_RESULT' AND actual_shares IS NOT NULL
    ORDER BY log_time DESC LIMIT 1
""", (cash_task_id,))

# 计算剩余份额
remaining_shares = original_target - cash_bought_shares
```

### 3. 修改现金不足处理逻辑

在 `execute_buy_cash_task` 函数中，当现金不足时：

```python
# 记录实际买入股数为0（用于融资买入任务计算剩余份额）
g_trade_task_callback_handler.record_execution_result(
    task_id=task_id,
    actual_shares=0,
    actual_price=current_price,
    actual_amount=0.0
)
```

### 4. 修改成交回调处理逻辑

在 `process_deal_callback_with_uuid` 函数中，添加执行结果记录：

```python
# 记录执行结果到 trade_task_log 表（用于融资买入任务计算剩余份额）
task_id = task['id']
self.record_execution_result(task_id, deal_shares, deal_price, deal_amount)
```

### 5. 删除不再使用的方法

删除 `get_original_target_shares` 方法，因为现在直接从任务参数获取。

## 修复效果验证

### 测试场景1：现金不足，无法买入最小单位
- **原始目标**：1000股
- **现金实际买入**：0股（现金不足）
- **剩余需融资买入**：1000股 ✅

### 测试场景2：现金部分买入
- **原始目标**：1000股
- **现金实际买入**：600股
- **剩余需融资买入**：400股 ✅

### 测试场景3：成交回调记录验证
- **原始目标**：1000股
- **现金实际买入**：700股（通过成交回调记录）
- **剩余需融资买入**：300股 ✅

## 修复优势

1. **数据一致性**：原始目标份额保存在任务参数中，不会被修改
2. **逻辑清晰**：融资买入任务直接从自己的参数获取原始目标
3. **向后兼容**：不影响现有的其他逻辑
4. **可靠性强**：即使现金买入失败，也能正确记录实际股数

## 涉及的文件

- `value_averaging_strategy.py`：主要修改文件
- `test_margin_buy_fix.py`：验证测试文件

## 修改总结

| 修改项 | 修改内容 | 行数 |
|--------|----------|------|
| 现金买入任务创建 | 添加 `original_target_shares` 到 `task_params` | 5146-5155 |
| 融资买入任务创建 | 添加 `original_target_shares` 到 `task_params` | 5164-5175 |
| 融资买入执行逻辑 | 从 `task_params` 获取原始目标，从日志表查询实际股数 | 5789-5829 |
| 现金不足处理 | 记录实际买入股数为0 | 5764-5770 |
| 成交回调处理 | 添加 `record_execution_result` 调用 | 6595-6597 |
| 清理代码 | 删除 `get_original_target_shares` 方法 | 5931-5954 |

## 结论

✅ **问题已完全解决**

通过保存原始目标份额到任务参数，并从日志表查询实际执行结果，成功解决了融资买入任务无法正确计算剩余份额的问题。修复后的逻辑更加清晰、可靠，能够正确处理各种现金买入情况。
